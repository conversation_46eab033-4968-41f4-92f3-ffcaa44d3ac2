package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.req.CreateProductBasedOnExistingRequest;
import com.knet.order.model.dto.resp.ProductOnSaleInfoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商品服务API客户端
 *
 * <AUTHOR>
 * @date 2025/09/02
 * @description: 超卖替换功能中订单服务调用商品服务的Feign客户端，提供商品状态检查、商品创建和锁定等功能
 */

/**
 * <AUTHOR>
 * @date 2025/2/14 11:01
 * @description: 商品服务api
 */
@FeignClient(name = "goods-services", path = "/goodServices/api")
public interface ApiGoodsServiceProvider {


    /**
     * 检查商品是否上架
     *
     * @param oneId oneId
     * @return HttpResult
     */
    @PostMapping("/inner/check-on-sale")
    HttpResult<Boolean> checkProductOnSale(@RequestParam("oneId") String oneId);

    /**
     * 根据oneId获取listingId
     *
     * @param oneId oneId
     * @return HttpResult
     */
    @PostMapping("/inner/get-listing-id")
    HttpResult<String> getListingIdByOneId(@RequestParam("oneId") String oneId);

    /**
     * 基于现有商品创建新商品
     *
     * @param request 创建请求
     * @return 新商品的listingId
     */
    @PostMapping("/inner/create-based-on-existing")
    HttpResult<String> createProductBasedOnExisting(@RequestBody CreateProductBasedOnExistingRequest request);


    /**
     * 将已上架商品锁定并标记为超卖替换
     *
     * @param oneId 商品oneId
     * @return HttpResult
     */
    @PostMapping("/inner/lock-for-oversell-replacement")
    HttpResult<Void> lockProductForOversellReplacement(@RequestParam("oneId") String oneId);

    /**
     * 获取商品上架状态和listingId信息
     *
     * @param oneId oneId
     * @return HttpResult
     */
    @PostMapping("/inner/get-product-onsale-info")
    HttpResult<ProductOnSaleInfoResp> getProductOnSaleInfo(@RequestParam("oneId") String oneId);
}
