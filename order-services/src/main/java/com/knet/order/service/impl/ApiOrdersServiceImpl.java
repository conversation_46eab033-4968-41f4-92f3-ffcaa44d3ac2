package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.dto.req.CreateProductBasedOnExistingRequest;
import com.knet.order.model.dto.req.ReplaceProductRequest;
import com.knet.order.model.dto.resp.ProductOnSaleInfoResp;
import com.knet.order.model.dto.resp.ReplaceProductResp;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.dto.third.resp.OrderAndLabelVo;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;
import com.knet.order.openfeign.ApiGoodsServiceProvider;
import com.knet.order.service.IApiOrdersService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:34
 * @description:
 */
@Slf4j
@Service
public class ApiOrdersServiceImpl implements IApiOrdersService {
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private SysOrderItemMapper sysOrderItemMapper;
    @Resource
    private ISysOrderProcessService iSysOrderProcessService;
    @Resource
    private ApiGoodsServiceProvider apiGoodsServiceProvider;

    @Override
    public List<SubOrderItemVo> getOrderItemsByPrentOrderId(String prentOrderId) {
        List<SysOrderItem> items = orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        Map<String, SubOrderItemVo> groupedItems = new HashMap<>(12);
        for (SysOrderItem item : items) {
            String groupKey = item.getSku() + "_" + item.getSize() + "_" + item.getPrice();
            if (groupedItems.containsKey(groupKey)) {
                SubOrderItemVo existingDto = groupedItems.get(groupKey);
                existingDto.setCount(existingDto.getCount() + item.getCount());
            } else {
                SubOrderItemVo newDto = SubOrderItemVo.create(item);
                groupedItems.put(groupKey, newDto);
            }
        }
        return new ArrayList<>(groupedItems.values());
    }

    @Override
    public SubOrderGroupVo getOrderGroupByPrentOrderId(String prentOrderId) {
        SysOrderGroup sysOrderGroup = orderGroupService.getOrderGroupByOrderId(prentOrderId);
        if (BeanUtil.isNotEmpty(sysOrderGroup)) {
            SubOrderGroupVo dto = new SubOrderGroupVo();
            dto.setParentOrderId(sysOrderGroup.getOrderId());
            dto.setStatus(sysOrderGroup.getStatus());
            dto.setTotalPrice(sysOrderGroup.getTotalAmount());
            return dto;
        }
        return null;
    }

    @Override
    public IPage<KnetB2bOrderQueryVo> getOrderItemsByPage(KnetB2bOrderQueryRequest request) {
        log.info("分页查询订单项，参数：{}", request);
        Page<OrderAndLabelVo> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<OrderAndLabelVo> db = sysOrderItemMapper.getOrderItemsByPage(page, request);
        return db.convert(KnetB2bOrderQueryVo::create);
    }

    @DistributedLock(key = "'KG_UPDATE_ORDER_STATUS:' + #request.hashCode()", expire = 1)
    @Override
    public boolean updateOrderStatus(UpdatedOrderRequest request) {
        return iSysOrderProcessService.smartUpdateOrderStatus(request);
    }

    @Override
    public SysOrderItem getOrderItemDetails(String itemId) {
        return orderItemService.getById(Long.parseLong(itemId));
    }

    @Override
    public List<SysOrderItem> queryOrderItemsByPrentOrderId(String prentOrderId) {
        return orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
    }

    @Loggable(value = "替换订单商品")
    @DistributedLock(key = "'replace:product:' + #request.itemNo", expire = 30)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ReplaceProductResp replaceOrderProduct(ReplaceProductRequest request) {
        log.info("开始替换订单商品: itemNo={}, newOneId={}",
                request.getItemNo(), request.getNewOneId());
        SysOrderItem orderItem = orderItemService.getOrderItemByItemNo(request.getItemNo());
        if (BeanUtil.isEmpty(orderItem)) {
            throw new ServiceException("订单项不存在: " + request.getItemNo());
        }
        String oldOneId = orderItem.getOneId();
        String oldListingId = orderItem.getKnetListingId();
        HttpResult<ProductOnSaleInfoResp> onSaleInfoResult = apiGoodsServiceProvider.getProductOnSaleInfo(request.getNewOneId());
        if (!onSaleInfoResult.success()) {
            throw new ServiceException("获取商品上架状态信息失败: " + onSaleInfoResult.getMsg());
        }
        ProductOnSaleInfoResp onSaleInfo = onSaleInfoResult.getData();
        String newListingId;
        if (Boolean.TRUE.equals(onSaleInfo.getOnSale())) {
            // 如果已上架，直接使用返回的listingId并锁定商品，标记为超卖替换
            newListingId = onSaleInfo.getListingId();
            // 锁定商品并标记为超卖替换
            HttpResult<Void> lockResult = apiGoodsServiceProvider.lockProductForOversellReplacement(request.getNewOneId());
            if (!lockResult.success()) {
                throw new ServiceException("锁定商品失败: " + lockResult.getMsg());
            }
            log.info("商品已上架，获取到listingId: {}，已锁定并标记为超卖替换", newListingId);
        } else {
            // 如果未上架，基于原商品创建新商品（自动设置为LOCKED状态和超卖替换标记）
            newListingId = createNewProductBasedOnOld(oldOneId, request.getNewOneId());
            log.info("商品未上架，已创建新商品，newListingId: {}，状态为LOCKED，标记为超卖替换", newListingId);
        }
        // 更新订单项信息
        orderItemService.updateOrderItem(request.getItemNo(), request.getNewOneId(), newListingId);
        log.info("订单商品替换成功: itemNo={}, {}→{}, {}→{}",
                request.getItemNo(), oldOneId, request.getNewOneId(), oldListingId, newListingId);
        return ReplaceProductResp.builder()
                .itemNo(request.getItemNo())
                .oldOneId(oldOneId)
                .oldListingId(oldListingId)
                .newOneId(request.getNewOneId())
                .newListingId(newListingId)
                .success(true)
                .message("超卖替换成功")
                .build();
    }

    /**
     * 基于原商品创建新商品
     */
    private String createNewProductBasedOnOld(String oldOneId, String newOneId) {
        CreateProductBasedOnExistingRequest createRequest = CreateProductBasedOnExistingRequest.builder()
                .sourceOneId(oldOneId)
                .targetOneId(newOneId)
                .build();
        HttpResult<String> createResult = apiGoodsServiceProvider.createProductBasedOnExisting(createRequest);
        if (!createResult.success()) {
            throw new ServiceException("创建新商品失败: " + createResult.getMsg());
        }
        return createResult.getData();
    }

    @Override
    public List<String> checkOneIdConflicts(List<String> oneIds, String excludeOrderId) {
        if (oneIds == null || oneIds.isEmpty()) {
            return new ArrayList<>();
        }
        log.info("检查oneId冲突: oneIds={}, excludeOrderId={}", oneIds, excludeOrderId);
        // 查询这些oneId是否已被其他订单使用（排除已取消的订单）
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .in(SysOrderItem::getOneId, oneIds)
                .notIn(SysOrderItem::getStatus, KnetOrderItemStatus.CANCELLED, KnetOrderItemStatus.SYSTEM_CANCELLED, KnetOrderItemStatus.PAY_FAILED)
                .ne(SysOrderItem::getParentOrderId, excludeOrderId);
        List<SysOrderItem> conflictItems = orderItemService.list(queryWrapper);
        if (conflictItems.isEmpty()) {
            log.info("未发现oneId冲突: oneIds={}", oneIds);
            return new ArrayList<>();
        }
        // 提取冲突的oneId
        List<String> conflictOneIds = conflictItems.stream()
                .map(SysOrderItem::getOneId)
                .distinct()
                .toList();
        log.warn("发现oneId冲突: conflictOneIds={}, 冲突订单详情: {}",
                conflictOneIds,
                conflictItems.stream()
                        .collect(Collectors.groupingBy(SysOrderItem::getOneId,
                                Collectors.mapping(item -> item.getParentOrderId() + "(" + item.getStatus() + ")",
                                        Collectors.toList()))));
        return conflictOneIds;
    }
}
