package com.knet.order.exception;

import com.knet.common.exception.ServiceException;
import com.knet.order.model.dto.resp.InventoryCheckResponse;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存不足异常
 */
@Getter
public class InsufficientInventoryException extends ServiceException {

    /**
     * 库存不足的商品列表
     */
    private final List<InventoryCheckResponse.InsufficientInventoryItem> insufficientItems;

    public InsufficientInventoryException(String message, List<InventoryCheckResponse.InsufficientInventoryItem> insufficientItems) {
        super(message);
        this.insufficientItems = insufficientItems;
    }

    public InsufficientInventoryException(String message, Throwable cause, List<InventoryCheckResponse.InsufficientInventoryItem> insufficientItems) {
        super(message, cause);
        this.insufficientItems = insufficientItems;
    }

    /**
     * 获取库存不足的详细信息
     */
    public String getInsufficientItemsDetail() {
        if (insufficientItems == null || insufficientItems.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (InventoryCheckResponse.InsufficientInventoryItem item : insufficientItems) {
            if (!sb.isEmpty()) {
                sb.append("; ");
            }
            sb.append(item.getErrorMessage());
        }
        return sb.toString();
    }

    /**
     * 获取库存不足商品的数量
     */
    public int getInsufficientItemCount() {
        return insufficientItems != null ? insufficientItems.size() : 0;
    }
}
