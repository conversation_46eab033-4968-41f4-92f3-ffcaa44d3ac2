package com.knet.order.model.dto.req;

import com.knet.order.model.vo.OrderItemDataVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查请求DTO（order-services版本）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "库存检查请求")
public class InventoryCheckRequest {

    @Schema(description = "商品项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品项列表不能为空")
    @Valid
    private List<InventoryCheckItem> items;

    /**
     * 库存检查商品项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "库存检查商品项")
    public static class InventoryCheckItem {

        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "商品SKU不能为空")
        private String sku;

        @Schema(description = "尺码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "尺码不能为空")
        private String size;

        @Schema(description = "单价（策略价格，单位：美元）", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "单价不能为空")
        private String price;

        @Schema(description = "需要数量", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "需要数量不能为空")
        @Min(value = 1, message = "需要数量必须大于0")
        private Integer quantity;

        @Schema(description = "用户账号（用于排除用户自己的商品）")
        private String userAccount;
    }

    /**
     * 从已处理的订单商品数据构建库存检查请求
     * 使用原始价格（已去掉策略的价格）进行库存检查
     *
     * @param productGroupMap 已处理的商品分组数据
     * @param account         用户账号
     * @return 库存检查请求
     */
    public static InventoryCheckRequest buildFromOrderItemData(Map<String, List<OrderItemDataVo>> productGroupMap, String account) {
        List<InventoryCheckItem> checkItems = productGroupMap.values().stream()
                .flatMap(List::stream)
                .map(itemData -> InventoryCheckItem.builder()
                        .sku(itemData.getSku())
                        .size(itemData.getSize())
                        .price(itemData.getOriginalPrice().toString())
                        .quantity(itemData.getQuantity())
                        .userAccount(account)
                        .build())
                .toList();
        return InventoryCheckRequest.builder().items(checkItems).build();
    }
}
