package com.knet.order.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查响应DTO（order-services版本）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "库存检查响应")
public class InventoryCheckResponse {

    @Schema(description = "库存检查是否通过")
    private Boolean success;

    @Schema(description = "检查消息")
    private String message;

    @Schema(description = "库存不足的商品列表")
    private List<InsufficientInventoryItem> insufficientItems;

    /**
     * 库存不足商品项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "库存不足商品项")
    public static class InsufficientInventoryItem {

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "单价（美元）")
        private String price;

        @Schema(description = "需要数量")
        private Integer requiredQuantity;

        @Schema(description = "可用数量")
        private Integer availableQuantity;

        @Schema(description = "缺少数量")
        private Integer shortageQuantity;

        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 检查是否有库存不足的商品
     */
    public boolean hasInsufficientItems() {
        return insufficientItems != null && !insufficientItems.isEmpty();
    }

    /**
     * 获取库存不足的详细信息
     */
    public String getInsufficientItemsDetail() {
        if (!hasInsufficientItems()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (InsufficientInventoryItem item : insufficientItems) {
            if (!sb.isEmpty()) {
                sb.append("; ");
            }
            sb.append(item.getErrorMessage());
        }
        return sb.toString();
    }
}
