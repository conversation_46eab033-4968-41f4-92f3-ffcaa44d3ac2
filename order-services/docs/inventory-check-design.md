# 订单创建前库存校验设计文档

## 概述

本文档描述了在用户通过购物车创建订单前增加库存校验逻辑的完整设计方案。该功能旨在避免无效订单的创建，在订单创建前检查商品库存是否充足，如果库存不足则明确告知用户具体哪个商品库存不足。

## 业务需求

### 核心需求
- 在订单创建前进行库存校验
- 检查所有订单商品的库存是否充足
- 库存不足时，明确告知用户哪个商品库存不足
- 避免创建无效订单，提升用户体验

### 功能特点
- **批量检查**：一次性检查订单中所有商品的库存
- **详细反馈**：提供具体的库存不足信息（SKU、尺码、需要数量、可用数量等）
- **性能优化**：减少网络调用次数，提高响应速度
- **异常处理**：完善的错误处理和日志记录机制

## 系统架构

### 整体架构图

```mermaid
sequenceDiagram
    participant User as 用户
    participant OrderSvc as 订单服务
    participant GoodsSvc as 商品服务
    participant DB as 数据库

    User->>OrderSvc: 1. 创建订单请求
    OrderSvc->>OrderSvc: 2. 验证请求数据
    OrderSvc->>GoodsSvc: 3. 批量库存检查
    GoodsSvc->>DB: 4. 查询商品库存
    DB-->>GoodsSvc: 5. 返回库存信息
    GoodsSvc-->>OrderSvc: 6. 返回检查结果
    
    alt 库存充足
        OrderSvc->>OrderSvc: 7a. 继续创建订单
        OrderSvc-->>User: 8a. 返回订单创建成功
    else 库存不足
        OrderSvc-->>User: 7b. 返回库存不足错误
    end
```

### 服务职责

#### 订单服务 (order-services)
- 在订单创建流程中集成库存校验
- 构建库存检查请求
- 处理库存检查结果
- 抛出库存不足异常

#### 商品服务 (goods-services)
- 提供批量库存检查接口
- 执行库存查询逻辑
- 返回详细的库存检查结果

## 技术实现

### 1. 数据结构设计

#### 库存检查请求 (InventoryCheckRequest)
```java
public class InventoryCheckRequest {
    private List<InventoryCheckItem> items;
    
    public static class InventoryCheckItem {
        private String sku;           // 商品SKU
        private String size;          // 尺码
        private String price;         // 单价（策略价格）
        private Integer quantity;     // 需要数量
        private String userAccount;   // 用户账号（排除用户自己的商品）
    }
}
```

#### 库存检查响应 (InventoryCheckResponse)
```java
public class InventoryCheckResponse {
    private Boolean success;                              // 检查是否通过
    private String message;                               // 检查消息
    private List<InsufficientInventoryItem> insufficientItems; // 库存不足商品列表
    
    public static class InsufficientInventoryItem {
        private String sku;                // 商品SKU
        private String size;               // 尺码
        private String price;              // 单价
        private Integer requiredQuantity;  // 需要数量
        private Integer availableQuantity; // 可用数量
        private Integer shortageQuantity;  // 缺少数量
        private String errorMessage;       // 错误信息
    }
}
```

### 2. 接口设计

#### 商品服务接口
```java
@PostMapping("/api/inventory/check")
HttpResult<InventoryCheckResponse> checkInventory(@RequestBody InventoryCheckRequest request);
```

#### OpenFeign客户端
```java
@FeignClient(name = "goods-services", path = "/goodServices/api")
public interface ApiGoodsServiceProvider {
    @PostMapping("/inventory/check")
    HttpResult<InventoryCheckResponse> checkInventory(@RequestBody InventoryCheckRequest request);
}
```

### 3. 核心实现逻辑

#### 库存检查服务实现
```java
@Service
public class InventoryCheckServiceImpl implements IInventoryCheckService {
    
    @Override
    public InventoryCheckResponse checkInventory(InventoryCheckRequest request) {
        // 1. 遍历所有商品项
        // 2. 对每个商品项进行库存检查
        // 3. 收集库存不足的商品信息
        // 4. 返回检查结果
    }
    
    private InventoryCheckResponse.InsufficientInventoryItem checkSingleItemInventory(
            InventoryCheckRequest.InventoryCheckItem item) {
        // 1. 价格转换（策略价格 -> 原始价格）
        // 2. 查询可用库存
        // 3. 比较需要数量和可用数量
        // 4. 返回库存不足信息（如果不足）
    }
}
```

#### 订单创建流程集成
```java
@Transactional(rollbackFor = Exception.class)
public CreateOrderResponse createOrderFromCart(CreateOrderRequest request) {
    try {
        // 1. 验证请求数据并按商品分组
        Map<String, List<OrderItemDataVo>> productGroupMap = validateAndGroupOrderItemsFromRequest(request);
        
        // 2. 库存校验 - 在创建订单前检查库存是否充足
        checkInventoryBeforeCreateOrder(request);
        
        // 3. 创建母订单记录
        // 4. 为每个商品创建子订单和明细
        // 5. 发送订单创建事件
        // ...
    } catch (InsufficientInventoryException e) {
        // 库存不足异常处理
        throw e;
    } catch (Exception e) {
        // 其他异常处理
        throw new ServiceException("订单创建失败: " + e.getMessage());
    }
}
```

### 4. 异常处理机制

#### 库存不足异常类
```java
public class InsufficientInventoryException extends ServiceException {
    private final List<InventoryCheckResponse.InsufficientInventoryItem> insufficientItems;
    
    public String getInsufficientItemsDetail() {
        // 返回详细的库存不足信息
    }
}
```

#### 异常处理流程
1. **库存检查失败** → 抛出 `InsufficientInventoryException`
2. **服务调用异常** → 抛出 `ServiceException`
3. **未知异常** → 记录日志并抛出通用异常

## 关键技术点

### 1. 价格处理
- **策略价格转换**：前端传入的是策略价格，需要转换为原始价格进行库存匹配
- **价格一致性**：确保库存检查和实际锁定使用相同的价格转换逻辑

### 2. 库存查询优化
- **批量查询**：一次性检查所有商品，减少数据库查询次数
- **索引优化**：基于SKU、价格、规格、状态的复合索引
- **状态过滤**：只查询 `ON_SALE` 状态的商品

### 3. 并发处理
- **分布式锁**：库存锁定时使用分布式锁防止并发问题
- **乐观锁**：商品状态更新时使用乐观锁机制
- **事务隔离**：合适的事务隔离级别避免脏读

### 4. 性能考虑
- **缓存策略**：可考虑对热门商品的库存信息进行缓存
- **异步处理**：库存检查可以考虑异步化处理
- **连接池**：合理配置数据库连接池参数

## 部署配置

### 1. 服务配置
```yaml
# goods-services application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# order-services application.yml
feign:
  client:
    config:
      goods-services:
        connectTimeout: 5000
        readTimeout: 10000
```

### 2. 数据库索引
```sql
-- 商品表库存查询优化索引
CREATE INDEX idx_knet_product_inventory 
ON knet_product (sku, price, spec, status, create_time);

-- 排除用户商品的索引
CREATE INDEX idx_knet_product_source 
ON knet_product (source, sku, price, spec, status);
```

## 监控和日志

### 1. 关键指标监控
- **库存检查成功率**：监控库存检查的成功率
- **库存不足频率**：统计库存不足的频率和商品分布
- **响应时间**：监控库存检查接口的响应时间
- **错误率**：监控服务调用的错误率

### 2. 日志记录
```java
// 库存检查开始
log.info("开始库存校验，用户ID: {}, 商品种类数: {}", userId, itemCount);

// 库存检查结果
log.info("库存校验通过，用户ID: {}", userId);
log.warn("库存校验失败: {}", errorMessage);

// 异常情况
log.error("库存检查服务调用失败: {}", errorMessage);
log.error("库存校验异常: {}", e.getMessage(), e);
```

## 测试策略

### 1. 单元测试
- **库存检查服务测试**：测试各种库存情况
- **异常处理测试**：测试各种异常场景
- **价格转换测试**：测试价格转换逻辑

### 2. 集成测试
- **服务间调用测试**：测试order-services调用goods-services
- **数据库查询测试**：测试库存查询的准确性
- **并发测试**：测试高并发场景下的表现

### 3. 性能测试
- **压力测试**：测试高并发下的性能表现
- **响应时间测试**：测试接口响应时间
- **资源使用测试**：测试CPU、内存使用情况

## 上线计划

### 1. 灰度发布
- **阶段1**：小流量灰度测试（5%流量）
- **阶段2**：中等流量测试（20%流量）
- **阶段3**：全量发布（100%流量）

### 2. 回滚方案
- **功能开关**：通过配置开关控制库存检查功能
- **快速回滚**：出现问题时快速回滚到原有逻辑
- **数据恢复**：确保数据一致性和完整性

## 总结

本库存校验系统通过在订单创建前进行库存检查，有效避免了无效订单的创建，提升了用户体验。系统设计考虑了性能、并发、异常处理等多个方面，确保了功能的稳定性和可靠性。

### 主要优势
1. **用户体验提升**：提前告知库存不足，避免用户创建无效订单
2. **系统性能优化**：批量检查减少网络调用，提高响应速度
3. **错误处理完善**：详细的错误信息和异常处理机制
4. **扩展性良好**：模块化设计，便于后续功能扩展

### 后续优化方向
1. **缓存优化**：对热门商品库存信息进行缓存
2. **预警机制**：库存不足时的预警和补货提醒
3. **智能推荐**：库存不足时推荐相似商品
4. **实时库存**：考虑实时库存更新机制

## 修改记录

本文档记录了根据用户反馈进行的重要修改：

### 1. 使用 HttpResult.success() 方法
- **修改前**：使用 `result.getCode() != 200` 判断请求是否成功
- **修改后**：使用 `result.success()` 方法判断请求是否成功
- **原因**：HttpResult 类提供了 success() 方法，更加简洁和语义化

### 2. 提取 buildInventoryCheckRequest 方法
- **修改前**：在 SysOrderProcessServiceImpl 中实现构建库存检查请求的逻辑
- **修改后**：将方法提取到 InventoryCheckRequest 类中作为静态方法 `buildFromOrderItemData()`
- **原因**：更好的代码组织，符合单一职责原则

### 3. 利用已处理的价格转换
- **修改前**：在 goods-services 中重复进行策略价格到原始价格的转换
- **修改后**：利用 order-services 中 `validateAndGroupOrderItemsFromRequest()` 方法已经处理过的原始价格
- **原因**：避免重复实现，提高代码复用性和一致性

### 4. 全局异常处理
- **修改前**：InsufficientInventoryException 没有被全局异常处理捕获
- **修改后**：在 GlobalExceptionHandler 中添加了专门的异常处理方法
- **原因**：确保库存不足异常能够被正确处理并返回友好的错误信息

### 5. 编译问题修复
- **修改前**：使用 `result.getMessage()` 方法（不存在）
- **修改后**：使用 `result.getMsg()` 方法
- **原因**：HttpResult 类的消息字段是 `msg`，不是 `message`

### 6. 编译验证
- **验证结果**：`mvn clean install -DskipTests` 执行成功
- **状态**：所有模块编译通过，无编译错误
