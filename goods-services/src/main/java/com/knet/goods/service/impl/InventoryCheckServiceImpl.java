package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.enums.ProductStatus;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.req.InventoryCheckRequest;
import com.knet.goods.model.dto.resp.InventoryCheckResponse;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.service.IInventoryCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/03
 * @description: 库存检查服务实现
 */
@Slf4j
@Service
public class InventoryCheckServiceImpl implements IInventoryCheckService {

    @Resource
    private KnetProductMapper knetProductMapper;

    @Override
    public InventoryCheckResponse checkInventory(InventoryCheckRequest request) {
        log.info("开始批量库存检查，商品种类数: {}", request.getItems().size());
        List<InventoryCheckResponse.InsufficientInventoryItem> insufficientItems = new ArrayList<>();
        for (InventoryCheckRequest.InventoryCheckItem item : request.getItems()) {
            try {
                // 检查单个商品的库存
                InventoryCheckResponse.InsufficientInventoryItem insufficientItem = checkSingleItemInventory(item);
                if (insufficientItem != null) {
                    insufficientItems.add(insufficientItem);
                }
            } catch (Exception e) {
                log.error("检查商品库存失败: sku={}, size={}, price={}, error={}",
                        item.getSku(), item.getSize(), item.getPrice(), e.getMessage());
                // 添加异常情况到库存不足列表
                InventoryCheckResponse.InsufficientInventoryItem errorItem =
                        InventoryCheckResponse.InsufficientInventoryItem.builder()
                                .sku(item.getSku())
                                .size(item.getSize())
                                .price(item.getPrice())
                                .requiredQuantity(item.getQuantity())
                                .availableQuantity(0)
                                .shortageQuantity(item.getQuantity())
                                .errorMessage("库存检查异常: " + e.getMessage())
                                .build();
                insufficientItems.add(errorItem);
            }
        }
        if (CollUtil.isEmpty(insufficientItems)) {
            log.info("库存检查通过，所有商品库存充足");
            return InventoryCheckResponse.success();
        } else {
            log.warn("库存检查失败，{}个商品库存不足", insufficientItems.size());
            return InventoryCheckResponse.failure("部分商品库存不足", insufficientItems);
        }
    }

    /**
     * 检查单个商品项的库存
     *
     * @param item 商品项
     * @return 如果库存不足返回不足信息，否则返回null
     */
    private InventoryCheckResponse.InsufficientInventoryItem checkSingleItemInventory(InventoryCheckRequest.InventoryCheckItem item) {
        Long originalPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        log.debug("库存检查: SKU={}, Size={}, 原始价格={}美分", item.getSku(), item.getSize(), originalPriceCents);
        // 查询可用库存（排除用户自己的商品）
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(KnetProduct::getSku, item.getSku())
                .eq(KnetProduct::getPrice, originalPriceCents)
                .eq(KnetProduct::getSpec, item.getSize())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .ne(StrUtil.isNotBlank(item.getUserAccount()), KnetProduct::getSource, item.getUserAccount());
        int availableQuantity = knetProductMapper.selectCount(queryWrapper).intValue();
        log.debug("库存检查结果: SKU={}, Size={}, 需要数量={}, 可用数量={}", item.getSku(), item.getSize(), item.getQuantity(), availableQuantity);
        if (availableQuantity < item.getQuantity()) {
            int shortageQuantity = item.getQuantity() - availableQuantity;
            return InventoryCheckResponse.InsufficientInventoryItem.builder()
                    .sku(item.getSku())
                    .size(item.getSize())
                    .price(item.getPrice())
                    .requiredQuantity(item.getQuantity())
                    .availableQuantity(availableQuantity)
                    .shortageQuantity(shortageQuantity)
                    .errorMessage(String.format("商品 %s 尺码 %s 库存不足，需要 %d 个，可用 %d 个，缺少 %d 个",
                            item.getSku(), item.getSize(), item.getQuantity(), availableQuantity, shortageQuantity))
                    .build();
        }
        // 库存充足
        return null;
    }
}
